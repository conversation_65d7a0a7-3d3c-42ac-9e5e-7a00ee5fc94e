using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Graph;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using Azure.Storage.Blobs;

namespace PasswordHistoryValidator;

public class UtilityFunction : BaseFunctionService
{
    private readonly ILogger<UtilityFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly RateLimitService _rateLimitService;
    private readonly IConfiguration _configuration;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly EntraOptions _entraOptions;

    public UtilityFunction(
        ILogger<UtilityFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        RateLimitService rateLimitService,
        IConfiguration configuration,
        BlobServiceClient blobServiceClient,
        GraphServiceClient graphServiceClient,
        IOptions<EntraOptions> entraOptions,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitService = rateLimitService;
        _configuration = configuration;
        _blobServiceClient = blobServiceClient;
        _graphServiceClient = graphServiceClient;
        _entraOptions = entraOptions.Value;
    }

    [Function("UtilityService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options", Route = null)] HttpRequestData req,
        FunctionContext executionContext,
        CancellationToken cancellationToken)
    {
        var correlationId = Guid.NewGuid().ToString("N")[..8];
        var logger = executionContext.GetLogger<UtilityFunction>();

        try
        {
            logger.LogInformation("Utility service triggered [CorrelationId: {CorrelationId}]", correlationId);
            logger.LogInformation("Request URL: {Url} [CorrelationId: {CorrelationId}]", req.Url, correlationId);
            logger.LogInformation("Request Method: {Method} [CorrelationId: {CorrelationId}]", req.Method, correlationId);


            if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
            {
                logger.LogInformation("Handling OPTIONS request [CorrelationId: {CorrelationId}]", correlationId);
                return CreateCorsResponse(req);
            }


            var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
            var operation = query["operation"] ?? string.Empty;

            logger.LogInformation("Processing operation: {Operation} [CorrelationId: {CorrelationId}]", operation, correlationId);


            var response = operation.ToLowerInvariant() switch
            {
                "health" => await HandleHealthCheck(req, correlationId, cancellationToken),
                "config-check" => await HandleConfigurationCheck(req, correlationId, cancellationToken),
                "cleanup-tokens" => await HandleTokenCleanup(req, correlationId, cancellationToken),
                "stats" => await HandleSystemStats(req, correlationId, cancellationToken),
                "test-graph" => await HandleGraphApiTest(req, correlationId, cancellationToken),
                "list-duplicates" => await HandleListDuplicateUsers(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };

            logger.LogInformation("Operation {Operation} completed [CorrelationId: {CorrelationId}]", operation, correlationId);
            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Utility service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleHealthCheck(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var healthStatus = new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                version = "3.0.0-simplified",
                services = new
                {
                    passwordHistoryService = await CheckPasswordHistoryServiceHealth(cancellationToken),
                    rateLimitingService = _rateLimitService.GetHealthStatus(),
                    configurationService = "healthy", // Simplified - DI validates config on startup
                    blobStorage = await CheckBlobStorageHealth(cancellationToken)
                },
                configuration = new
                {
                    maxHistoryCount = _configuration.GetValue<int>("PasswordHistory:MaxCount", 12),
                    workFactor = _configuration.GetValue<int>("PasswordHistory:WorkFactor", 12),
                    rateLimitPerMinute = _configuration.GetValue<int>("RateLimit:MaxRequestsPerMinute", 60),
                    hasSendGridKey = !string.IsNullOrEmpty(_configuration["SendGrid:ApiKey"]),
                    functionsRuntime = _configuration["FUNCTIONS_WORKER_RUNTIME"] ?? "not-set"
                }
            };

            return await CreateSuccessResponse(req, "Health check completed", healthStatus, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check error");
            return await CreateErrorResponse(req, "Health check failed", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleConfigurationCheck(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var configStatus = new
            {
                isValid = true, // Simplified - DI validates config on startup
                missingSettings = new List<string>(), // Empty - strongly-typed options handle this
                timestamp = DateTime.UtcNow,
                recommendations = new List<string> { "Configuration is managed via strongly-typed options" }
            };

            return await CreateSuccessResponse(req, "Configuration check completed", configStatus, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during configuration check");
            return await CreateErrorResponse(req, "Configuration check failed", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleTokenCleanup(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var cleanupResults = await CleanupExpiredTokens(cancellationToken);

            return await CreateSuccessResponse(req, "Token cleanup completed", new
            {
                tokensRemoved = cleanupResults.TokensRemoved,
                tokensProcessed = cleanupResults.TokensProcessed,
                timestamp = DateTime.UtcNow
            }, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token cleanup");
            return await CreateErrorResponse(req, "Token cleanup failed", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleSystemStats(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var stats = await GetSystemStatistics(cancellationToken);

            return await CreateSuccessResponse(req, "System statistics retrieved", stats, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system stats");
            return await CreateErrorResponse(req, "Failed to get system statistics", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleGraphApiTest(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {

            var tenantId = _entraOptions.TenantId;
            var clientId = _entraOptions.ClientId;
            var clientSecret = _entraOptions.ClientSecret;

            var testResult = new
            {
                configurationStatus = new
                {
                    tenantIdConfigured = !string.IsNullOrEmpty(tenantId),
                    clientIdConfigured = !string.IsNullOrEmpty(clientId),
                    clientSecretConfigured = !string.IsNullOrEmpty(clientSecret),
                    tenantIdValue = string.IsNullOrEmpty(tenantId) ? "NOT_SET" : $"{tenantId.Substring(0, 8)}...",
                    clientIdValue = string.IsNullOrEmpty(clientId) ? "NOT_SET" : $"{clientId.Substring(0, 8)}..."
                },
                graphApiTest = "NOT_TESTED",
                error = null as string
            };


            if (string.IsNullOrEmpty(tenantId) || string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret))
            {
                testResult = testResult with
                {
                    graphApiTest = "SKIPPED_MISSING_CONFIG",
                    error = "Missing Entra External ID configuration"
                };
                return await CreateSuccessResponse(req, "Graph API test completed", testResult, HttpStatusCode.OK, correlationId);
            }


            try
            {
                var credential = new Azure.Identity.ClientSecretCredential(tenantId, clientId, clientSecret);
                var graphServiceClient = new Microsoft.Graph.GraphServiceClient(credential);


                var organization = await graphServiceClient.Organization.GetAsync(cancellationToken: cancellationToken);

                testResult = testResult with
                {
                    graphApiTest = "SUCCESS",
                    error = null
                };


            }
            catch (Exception graphEx)
            {
                _logger.LogError(graphEx, "Graph API test failed");
                testResult = testResult with
                {
                    graphApiTest = "FAILED",
                    error = graphEx.Message
                };
            }

            return await CreateSuccessResponse(req, "Graph API test completed", testResult, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Graph API test");
            return await CreateErrorResponse(req, "Graph API test failed", correlationId);
        }
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }



    private async Task<string> CheckPasswordHistoryServiceHealth(CancellationToken cancellationToken)
    {
        try
        {

            var testResult = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                "health-check", "test-user", "test-password-that-should-not-exist", cancellationToken);

            return testResult.IsSuccess ? "healthy" : "degraded";
        }
        catch
        {
            return "unhealthy";
        }
    }



    private async Task<string> CheckBlobStorageHealth(CancellationToken cancellationToken)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient("passwordhistory");
            await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);
            return "healthy";
        }
        catch
        {
            return "unhealthy";
        }
    }



    private async Task<CleanupResults> CleanupExpiredTokens(CancellationToken cancellationToken)
    {
        var results = new CleanupResults();

        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient("resettokens");

            await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                results.TokensProcessed++;

                try
                {
                    var blobClient = containerClient.GetBlobClient(blobItem.Name);
                    var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                    var tokenDataJson = downloadResult.Value.Content.ToString();
                    var tokenData = JsonSerializer.Deserialize<ResetTokenData>(tokenDataJson, JsonOptions);

                    if (tokenData != null && (tokenData.ExpiresUtc < DateTime.UtcNow || tokenData.Used))
                    {
                        await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
                        results.TokensRemoved++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing token blob {BlobName} during cleanup", blobItem.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token cleanup");
            throw;
        }

        return results;
    }

    private async Task<object> GetSystemStatistics(CancellationToken cancellationToken)
    {
        try
        {
            var passwordHistoryContainer = _blobServiceClient.GetBlobContainerClient("passwordhistory");
            var resetTokensContainer = _blobServiceClient.GetBlobContainerClient("resettokens");

            var passwordHistoryCount = 0;
            var resetTokensCount = 0;
            var activeTokensCount = 0;


            await foreach (var blobItem in passwordHistoryContainer.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                passwordHistoryCount++;
            }


            await foreach (var blobItem in resetTokensContainer.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                resetTokensCount++;

                try
                {
                    var blobClient = resetTokensContainer.GetBlobClient(blobItem.Name);
                    var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                    var tokenDataJson = downloadResult.Value.Content.ToString();
                    var tokenData = JsonSerializer.Deserialize<ResetTokenData>(tokenDataJson, JsonOptions);

                    if (tokenData != null && !tokenData.Used && tokenData.ExpiresUtc > DateTime.UtcNow)
                    {
                        activeTokensCount++;
                    }
                }
                catch
                {

                }
            }

            return new
            {
                passwordHistoryEntries = passwordHistoryCount,
                totalResetTokens = resetTokensCount,
                activeResetTokens = activeTokensCount,
                expiredTokens = resetTokensCount - activeTokensCount,
                timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system statistics");
            throw;
        }
    }



    private async Task<HttpResponseData> HandleListDuplicateUsers(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<AuthRequest>(requestBody, JsonOptions);

            if (data == null || string.IsNullOrWhiteSpace(data.Email))
            {
                return await CreateErrorResponse(req, "Email is required", correlationId);
            }

            var applicationName = data.ApplicationName ?? "Default Application";
            var appUsers = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department", "createdDateTime" };
                    requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                }, cancellationToken);

            var allUsers = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department", "createdDateTime" };
                    requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                }, cancellationToken);

            var appUserList = appUsers?.Value?.Select(u => new
            {
                id = u.Id,
                email = u.Mail,
                userPrincipalName = u.UserPrincipalName,
                displayName = u.DisplayName,
                department = u.Department,
                createdDateTime = u.CreatedDateTime,
                scope = "current-application"
            }).ToList();

            var allUserList = allUsers?.Value?.Select(u => new
            {
                id = u.Id,
                email = u.Mail,
                userPrincipalName = u.UserPrincipalName,
                displayName = u.DisplayName,
                department = u.Department,
                createdDateTime = u.CreatedDateTime,
                scope = u.Department?.StartsWith(applicationName) == true ? "current-application" : "other-application"
            }).ToList();

            var appUserCount = appUserList?.Count ?? 0;
            var totalUserCount = allUserList?.Count ?? 0;

            var responseAppUsers = appUserList != null ? appUserList.Cast<object>().ToList() : new List<object>();
            var responseAllUsers = allUserList != null ? allUserList.Cast<object>().ToList() : new List<object>();

            return await CreateSuccessResponse(req, "User list retrieved", new
            {
                email = data.Email,
                applicationName = applicationName,
                currentApplication = new
                {
                    userCount = appUserCount,
                    users = responseAppUsers,
                    hasDuplicates = appUserCount > 1
                },
                allApplications = new
                {
                    userCount = totalUserCount,
                    users = responseAllUsers,
                    hasMultipleApplications = totalUserCount > appUserCount
                }
            }, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing duplicate users [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Failed to list users", correlationId);
        }
    }

    private class CleanupResults
    {
        public int TokensProcessed { get; set; }
        public int TokensRemoved { get; set; }
    }
}
